﻿  main.c
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(243,13): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(923,12): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1630,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1711,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1727,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1783,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1821,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1890,40): warning C4305: “=”: 从“double”到“float”截断
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(2143,5): warning C4013: “NAV_function_presins”未定义；假设外部返回 int
E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(2101,21): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
  正在生成代码
  16 of 58 functions (27.6%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    1 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  tlh_ins632332_20231121.vcxproj -> E:\INS_Code\组合算法后处理VS_升沉\组合算法后处理VS\POST_Raw_Version_LiGong\x64\Release\tlh_ins632332_20231121.exe
